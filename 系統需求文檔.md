# ROBOT_INVEST 保險系統機器人 - 系統需求文檔

## 1. 項目概述

### 1.1 項目背景
- **項目名稱**: ROBOT_INVEST (bot-base)
- **項目類型**: 保險公司債券信息自動抓取機器人系統
- **主要用途**: 從97財經網站(https://www.97caijing.com/)自動抓取債券價格信息
- **開發公司**: CSCI (中海外)
- **技術架構**: Spring Boot + MyBatis + Selenium WebDriver

### 1.2 系統目標
- 自動化債券價格數據抓取
- 定時任務調度執行
- 數據持久化存儲
- 多數據源支持
- 異常處理和日誌記錄

## 2. 技術架構

### 2.1 核心技術棧
- **後端框架**: Spring Boot 2.4.2
- **Java版本**: JDK 1.8
- **數據庫**: MariaDB + SQL Server
- **ORM框架**: MyBatis 2.1.4
- **自動化工具**: Selenium WebDriver 4.0.0-alpha-6
- **緩存**: Redis
- **消息隊列**: Apache Pulsar 2.7.1
- **文檔**: Swagger 2.9.2
- **Excel處理**: EasyExcel 3.1.1 + Apache POI 4.1.2
- **OCR識別**: Tess4j 5.4.0

### 2.2 項目結構
```
src/main/java/com/csci/exam/
├── Application.java              # 主應用程序入口
├── annotation/                   # 自定義註解
├── aspectj/                     # AOP切面
├── bot/                         # 機器人核心模塊
├── common/                      # 公共常量和工具
├── configuration/               # 配置類
├── controller/                  # REST控制器
├── exception/                   # 異常處理
├── job/                        # 定時任務
├── log/                        # 日誌相關
├── mapper/                     # MyBatis映射器
├── model/                      # 數據模型
├── service/                    # 業務服務層
└── util/                       # 工具類
```

## 3. 功能需求

### 3.1 核心業務功能

#### 3.1.1 債券價格自動抓取
- **功能描述**: 自動從97財經網站抓取指定債券的實時價格信息
- **支持債券列表**:
  - USY3422VCT36
  - XS0852986313
  - XS1075180379
  - XS1788513734
  - XS1596795358
  - XS1750118462
  - XS1974522937
  - XS1958532829

#### 3.1.2 定時任務調度
- **執行時間**: 每天早上8:00-9:00，每10分鐘執行一次
- **Cron表達式**: `0 0,10,20,40,50 08 * * ?`
- **任務內容**: 遍歷債券列表，逐一抓取價格數據

#### 3.1.3 數據去重機制
- **功能**: 檢查當日是否已存在相同債券的價格記錄
- **邏輯**: 如果當日已有記錄，則跳過該債券的數據插入

### 3.2 數據管理功能

#### 3.2.1 多數據源支持
- **EXAM數據源**: MariaDB (pa_rpa/funds_auto)
- **INVEST數據源**: SQL Server (live_ilodata)
- **動態切換**: 通過@DS註解實現數據源動態切換

#### 3.2.2 數據模型
```sql
-- BondData表結構
CREATE TABLE BondData (
    ID INT IDENTITY(1,1) PRIMARY KEY,
    BondSin VARCHAR(50),        -- 債券代碼
    GetDate DATETIME,           -- 獲取時間
    Price VARCHAR(20),          -- 價格
    IsSend BIT,                 -- 是否已發送
    SendDate DATETIME           -- 發送時間
);
```

## 4. 非功能性需求

### 4.1 性能需求
- **響應時間**: 單次債券價格抓取 < 30秒
- **併發處理**: 支持多債券並行處理
- **數據庫連接池**: 最大20個連接，最小5個空閒連接

### 4.2 可靠性需求
- **異常重試**: 抓取失敗時自動重試
- **日誌記錄**: 完整的操作日誌和錯誤日誌
- **數據備份**: 支持數據庫事務回滾

### 4.3 安全性需求
- **數據庫連接**: 加密連接字符串
- **異常處理**: 統一異常處理機制
- **參數驗證**: 防止SQL注入和XSS攻擊

## 5. 系統集成

### 5.1 外部系統集成
- **97財經網站**: 債券價格數據源
- **消息中心**: 通知和告警服務
- **Pulsar消息隊列**: 異步消息處理

### 5.2 環境配置
- **開發環境**: localhost:8090
- **生產環境**: bot.csci.com.hk:18090
- **Selenium Grid**: 遠程WebDriver服務

## 6. 部署需求

### 6.1 服務器要求
- **操作系統**: Windows Server
- **Java環境**: JDK 1.8+
- **瀏覽器**: Chrome + ChromeDriver
- **網絡**: 可訪問97財經網站

### 6.2 數據庫要求
- **MariaDB**: 10.x版本
- **SQL Server**: 2016+版本
- **Redis**: 6.x版本

## 7. 監控和維護

### 7.1 日誌管理
- **日誌級別**: INFO, ERROR
- **日誌輪轉**: 每日輪轉，保留15天
- **日誌大小**: 單文件最大20MB

### 7.2 健康檢查
- **心跳檢測**: 每5秒輸出心跳信息
- **數據庫連接**: 定期檢查數據庫連接狀態
- **WebDriver狀態**: 監控Selenium服務狀態

## 8. 風險評估

### 8.1 技術風險
- **網站結構變更**: 97財經網站DOM結構變化可能導致抓取失敗
- **反爬蟲機制**: 目標網站可能增加反爬蟲措施
- **網絡穩定性**: 網絡中斷可能影響數據抓取

### 8.2 業務風險
- **數據準確性**: 需要驗證抓取數據的準確性
- **時效性**: 確保價格數據的實時性
- **合規性**: 遵守網站使用條款和法律法規

## 9. 擴展性考慮

### 9.1 功能擴展
- **支持更多債券**: 可配置化債券列表
- **多數據源**: 支持更多財經網站
- **數據分析**: 增加價格趨勢分析功能

### 9.2 技術擴展
- **微服務架構**: 拆分為獨立的微服務
- **容器化部署**: 支持Docker部署
- **雲原生**: 支持Kubernetes編排

## 10. 開發和測試

### 10.1 開發規範
- **代碼規範**: 遵循Java編碼規範
- **註釋要求**: 關鍵方法必須有詳細註釋
- **異常處理**: 統一異常處理機制

### 10.2 測試策略
- **單元測試**: 核心業務邏輯測試
- **集成測試**: 數據庫和外部服務集成測試
- **端到端測試**: 完整業務流程測試

---

**文檔版本**: 1.0  
**創建日期**: 2025-07-08  
**負責人**: 系統分析師  
**審核人**: 項目經理
