# ROBOT_INVEST 保險系統機器人 - 系統需求文檔

## 1. 項目概述

### 1.1 項目背景
- **項目名稱**: ROBOT_INVEST (bot-base)
- **項目類型**: 保險公司債券信息自動抓取機器人系統
- **主要用途**: 從97財經網站(https://www.97caijing.com/)自動抓取債券價格信息
- **開發公司**: CSCI (中海外)
- **技術架構**: Spring Boot + MyBatis + Selenium WebDriver

### 1.2 系統目標
- 自動化債券價格數據抓取
- 定時任務調度執行
- 數據持久化存儲
- 多數據源支持
- 異常處理和日誌記錄

## 2. 技術架構

### 2.1 核心技術棧
- **後端框架**: Spring Boot 2.4.2
- **Java版本**: JDK 1.8
- **數據庫**: MariaDB + SQL Server
- **ORM框架**: MyBatis 2.1.4
- **自動化工具**: Selenium WebDriver 4.0.0-alpha-6
- **緩存**: Redis
- **消息隊列**: Apache Pulsar 2.7.1
- **文檔**: Swagger 2.9.2
- **Excel處理**: EasyExcel 3.1.1 + Apache POI 4.1.2
- **OCR識別**: Tess4j 5.4.0

### 2.2 項目結構
```
src/main/java/com/csci/exam/
├── Application.java              # 主應用程序入口
├── annotation/                   # 自定義註解
├── aspectj/                     # AOP切面
├── bot/                         # 機器人核心模塊
├── common/                      # 公共常量和工具
├── configuration/               # 配置類
├── controller/                  # REST控制器
├── exception/                   # 異常處理
├── job/                        # 定時任務
├── log/                        # 日誌相關
├── mapper/                     # MyBatis映射器
├── model/                      # 數據模型
├── service/                    # 業務服務層
└── util/                       # 工具類
```

## 3. 功能需求

### 3.1 核心業務功能

#### 3.1.1 債券價格自動抓取
- **目標網站**: https://www.97caijing.com/
- **抓取債券列表**:
  - USY3422VCT36 (債券詳情ID: 101951)
  - XS0852986313 (債券詳情ID: 2793)
  - XS1075180379 (債券詳情ID: 2463)
  - XS1788513734 (債券詳情ID: 978)
  - XS1596795358 (債券詳情ID: 1451)
  - XS1750118462 (債券詳情ID: 1077)
  - XS1974522937 (債券詳情ID: 386)
  - XS1958532829 (債券詳情ID: 438)

#### 3.1.2 定時任務調度
- **執行時間**: 每天早上8:00-9:00，每10分鐘執行一次
- **Cron表達式**: `0 0,10,20,40,50 08 * * ?`
- **任務內容**: 遍歷債券列表，逐一抓取價格數據

#### 3.1.3 數據去重機制
- **功能**: 檢查當日是否已存在相同債券的價格記錄
- **邏輯**: 如果當日已有記錄，則跳過該債券的數據插入

### 3.2 數據管理功能

#### 3.2.1 多數據源支持
- **EXAM數據源**: MariaDB (pa_rpa/funds_auto)
- **INVEST數據源**: SQL Server (live_ilodata)
- **動態切換**: 通過@DS註解實現數據源動態切換

#### 3.2.2 數據模型
```sql
-- BondData表結構
CREATE TABLE BondData (
    ID INT IDENTITY(1,1) PRIMARY KEY,
    BondSin VARCHAR(50),        -- 債券代碼
    GetDate DATETIME,           -- 獲取時間
    Price VARCHAR(20),          -- 價格
    IsSend BIT,                 -- 是否已發送
    SendDate DATETIME           -- 發送時間
);
```

## 4. 系統架構設計

### 4.1 分層架構
```
┌─────────────────────────────────────┐
│           控制器層 (Controller)        │
├─────────────────────────────────────┤
│           服務層 (Service)            │
├─────────────────────────────────────┤
│         數據訪問層 (Mapper)            │
├─────────────────────────────────────┤
│           數據庫層 (Database)          │
└─────────────────────────────────────┘
```

### 4.2 核心組件

#### 4.2.1 機器人模塊 (bot)
- **IAutomatic**: 自動化接口定義
- **AbstractBaseAuto**: 抽象基礎自動化類
- **InvestBot**: 投資債券抓取機器人實現
- **CustomElement**: 自定義Web元素封裝

#### 4.2.2 配置模塊 (configuration)
- **DatasourceConfiguration**: 數據源配置
- **SwaggerConfig**: API文檔配置
- **GlobalExceptionHandler**: 全局異常處理
- **DynamicDataSource**: 動態數據源切換

#### 4.2.3 工具模塊 (util)
- **SpringContextUtil**: Spring上下文工具
- **RedisUtil**: Redis緩存工具
- **OcrUtils**: OCR識別工具
- **AutoUtils**: 自動化工具

## 5. 數據流程設計

### 5.1 債券價格抓取流程
```mermaid
graph TD
    A[定時任務啟動] --> B[遍歷債券列表]
    B --> C[初始化WebDriver]
    C --> D[打開97財經網站]
    D --> E[執行登錄操作]
    E --> F[導航到債券詳情頁]
    F --> G[抓取價格數據]
    G --> H[檢查當日是否已存在]
    H --> I{是否存在?}
    I -->|是| J[跳過該債券]
    I -->|否| K[保存到數據庫]
    K --> L[記錄日誌]
    J --> M[處理下一個債券]
    L --> M
    M --> N{是否還有債券?}
    N -->|是| C
    N -->|否| O[任務完成]
```

### 5.2 數據存儲流程
```mermaid
graph TD
    A[接收債券數據] --> B[創建BondPrice對象]
    B --> C[設置債券代碼]
    C --> D[設置價格信息]
    D --> E[設置獲取時間]
    E --> F[調用BondPriceService]
    F --> G[執行數據庫插入]
    G --> H[事務提交]
    H --> I[返回結果]
```

## 6. API接口設計

### 6.1 REST API端點
- **基礎路徑**: `/`
- **健康檢查**: `GET /` - 返回歡迎信息
- **Swagger文檔**: `/swagger-ui.html`

### 6.2 內部服務接口

#### 6.2.1 BondPriceService
```java
public interface BondPriceService {
    int insert(BondPrice record);           // 插入債券價格記錄
    List<BondPrice> getTodayData();         // 獲取當日數據
}
```

#### 6.2.2 BaseService
```java
public interface BaseService {
    ResultPage<Map> listByPage(String sql, int pageNo, int pageSize);
    List<Map> list(String sql);
    String selectTime();
}
```

## 7. 配置管理

### 7.1 環境配置
- **開發環境**: application-dev.yml
- **生產環境**: application-prod.yml
- **默認激活**: prod

### 7.2 關鍵配置項
```yaml
# 數據源配置
datasource:
  exam:
    hikari:
      jdbc-url: *****************************************
      username: fundsauto_prod
      password: '!csci3311#'
  invest:
    hikari:
      jdbc-url: **************************************************************
      username: common
      password: gn9gnahc14

# Chrome驅動配置
chrome:
  driver:
    path: C:/chromedriver.exe

# Redis配置
spring:
  redis:
    host: **********
    port: 6379
    password: Passw0rd
```

## 8. 安全與監控

### 8.1 安全措施
- **數據源隔離**: 通過@DS註解實現數據源動態切換
- **異常處理**: 全局異常處理機制
- **日誌記錄**: 完整的操作日誌記錄

### 8.2 監控機制
- **心跳檢測**: 每5秒執行一次心跳任務
- **日誌監控**: 通過logback進行日誌管理
- **錯誤通知**: 支持郵件通知機制

## 9. 部署與運維

### 9.1 部署要求
- **Java版本**: JDK 1.8+
- **Chrome瀏覽器**: 需要安裝Chrome和ChromeDriver
- **數據庫**: MariaDB + SQL Server
- **緩存**: Redis

### 9.2 運行方式
- **JAR包運行**: `java -jar bot-base-0.0.1-SNAPSHOT.jar`
- **端口**: 18090 (生產環境)
- **日誌路徑**: `./logs/`

## 10. 擴展性設計

### 10.1 可擴展點
- **新債券支持**: 通過修改債券列表配置
- **新數據源**: 通過@DS註解支持新的數據源
- **新抓取網站**: 通過實現IAutomatic接口

### 10.2 未來改進方向
- **配置外部化**: 將債券列表配置到外部文件
- **分佈式支持**: 支持多實例部署
- **實時監控**: 增加實時監控面板
- **數據分析**: 增加債券價格趨勢分析功能

## 11. 詳細技術實現

### 11.1 核心類詳細分析

#### 11.1.1 InvestBot類
```java
public class InvestBot extends AbstractBaseAuto {
    // 債券代碼映射到網站URL
    private static final Map<String, String> BOND_URL_MAP = Map.of(
        "USY3422VCT36", "https://www.97caijing.com/#/primaryMarket/bondDetail/101951",
        "XS0852986313", "https://www.97caijing.com/#/primaryMarket/bondDetail/2793",
        // ... 其他債券映射
    );

    // 主要流程: 打開網站 -> 登錄 -> 獲取數據 -> 保存記錄
    public void process() {
        openSite();
        login();
        getData();
        record();
    }
}
```

#### 11.1.2 BondPrice數據模型
```java
public class BondPrice {
    private LocalDateTime GetDate;    // 獲取時間
    private String Price;             // 價格
    private String Sin;               // 債券代碼
    private Integer ID;               // 主鍵ID
    private Boolean IsSend;           // 是否已發送
    private LocalDateTime SendDate;   // 發送時間
}
```

#### 11.1.3 動態數據源實現
```java
@Configuration
public class DatasourceConfiguration {
    @Bean
    public DataSource dataSource() {
        DynamicDataSource dataSource = new DynamicDataSource();
        dataSource.setDefaultTargetDataSource(investDatasource());

        Map<Object, Object> dsStore = new HashMap<>();
        dsStore.put(DatasourceContextEnum.EXAM, examDataSource());
        dsStore.put(DatasourceContextEnum.INVEST, investDatasource());
        dataSource.setTargetDataSources(dsStore);

        return dataSource;
    }
}
```

### 11.2 關鍵業務邏輯

#### 11.2.1 登錄流程
1. 點擊登錄按鈕打開登錄彈窗
2. 切換到新窗口
3. 輸入用戶名: 15814667251
4. 輸入密碼: 82503002
5. 點擊登錄按鈕
6. 處理可能的重複登錄提示

#### 11.2.2 數據抓取邏輯
1. 等待頁面加載完成
2. 使用XPath定位價格元素: `//*[@id="root"]//div[@_nk="hDaQ2t"]/div[2]`
3. 提取價格文本信息
4. 記錄到日誌

#### 11.2.3 數據保存邏輯
1. 檢查當日是否已有該債券的記錄
2. 如果不存在，創建新的BondPrice對象
3. 設置債券代碼、價格和獲取時間
4. 調用BondPriceService.insert()保存到數據庫

### 11.3 異常處理機制

#### 11.3.1 全局異常處理
```java
@ControllerAdvice
public class GlobalExceptionHandler {
    @ExceptionHandler({Exception.class})
    public ResultBean<?> handleException(Exception ex, WebRequest request) {
        if (ex instanceof ServiceException) {
            return new ResultBean<>(FAIL, ex.getLocalizedMessage());
        }
        if (ex instanceof DuplicateKeyException) {
            return new ResultBean<>(FAIL, "主鍵冲突，請不要添加重複的記錄");
        }
        return new ResultBean<>(FAIL, "出錯了，請聯系管理員解決");
    }
}
```

#### 11.3.2 自動化異常處理
- **元素查找失敗**: 重試機制，最多重試10次
- **網絡連接問題**: 自動重新啟動機器人
- **登錄失敗**: 記錄錯誤日誌並退出

### 11.4 工具類功能

#### 11.4.1 SpringContextUtil
- 提供靜態方法獲取Spring容器中的Bean
- 支持按類型和按名稱獲取Bean
- 在非Spring管理的類中訪問Spring容器

#### 11.4.2 RedisUtil
- 封裝Redis常用操作
- 支持String、Hash、List、Set、ZSet數據類型
- 提供過期時間設置和模糊查詢功能

#### 11.4.3 OcrUtils
- 支持Tesseract OCR識別
- 支持Python腳本OCR識別
- 用於處理驗證碼等圖像識別需求

## 12. 數據庫設計

### 12.1 表結構設計

#### 12.1.1 BondData表
```sql
CREATE TABLE [dbo].[BondData](
    [ID] [int] IDENTITY(1,1) NOT NULL,
    [BondSin] [varchar](50) NULL,
    [GetDate] [datetime] NULL,
    [Price] [varchar](20) NULL,
    [IsSend] [bit] NULL,
    [SendDate] [datetime] NULL,
    CONSTRAINT [PK_BondData] PRIMARY KEY CLUSTERED ([ID] ASC)
)
```

#### 12.1.2 索引設計
- **主鍵索引**: ID (自動創建)
- **業務索引**: BondSin + GetDate (建議創建，提高查詢效率)
- **時間索引**: GetDate (用於按日期查詢)

### 12.2 數據訪問層

#### 12.2.1 MyBatis映射
```xml
<mapper namespace="com.csci.exam.mapper.BondPriceMapper">
    <insert id="insert" parameterType="com.csci.exam.model.BondPrice">
        insert into BondData (BondSin, GetDate, Price)
        values (#{Sin,jdbcType=VARCHAR}, #{GetDate,jdbcType=TIMESTAMP}, #{Price,jdbcType=VARCHAR})
    </insert>

    <select id="getTodayData" resultType="com.csci.exam.model.BondPrice">
        SELECT ID, BondSin [Sin], GetDate, Price, IsSend, SendDate
        FROM dbo.BondData
        WHERE CONVERT(VARCHAR(10), GetDate, 120) = CONVERT(VARCHAR(10), GETDATE(), 120)
    </select>
</mapper>
```

## 13. 系統集成

### 13.1 外部系統集成

#### 13.1.1 97財經網站
- **網站地址**: https://www.97caijing.com/
- **登錄方式**: 手機號碼 + 密碼
- **數據獲取**: 通過Selenium WebDriver自動化操作

#### 13.1.2 消息隊列集成
- **消息中間件**: Apache Pulsar
- **服務地址**: pulsar://10.1.8.156:6650
- **主題配置**: persistent://public/default/bot-base-prod

#### 13.1.3 通知系統
- **郵件通知**: 支持多收件人郵件通知
- **消息中心**: 集成CSCI消息中心系統

### 13.2 第三方依賴

#### 13.2.1 Selenium WebDriver
- **版本**: 4.0.0-alpha-6
- **支持瀏覽器**: Chrome, Firefox, Edge, IE, Safari
- **遠程驅動**: 支持Selenium Grid分佈式執行

#### 13.2.2 數據庫驅動
- **MariaDB**: mariadb-java-client
- **SQL Server**: mssql-jdbc 8.2.2.jre8

## 14. 性能與優化

### 14.1 性能指標
- **數據抓取頻率**: 每10分鐘一次
- **單次執行時間**: 預計5-10分鐘
- **數據庫連接池**: 最大20個連接，最小5個空閒連接
- **頁面加載超時**: 20秒

### 14.2 優化策略
- **連接池優化**: 使用HikariCP高性能連接池
- **緩存策略**: 使用Redis緩存常用數據
- **異步處理**: 支持非阻塞式任務執行
- **資源管理**: 自動釋放WebDriver資源

## 15. 測試策略

### 15.1 單元測試
- **測試框架**: JUnit 5
- **覆蓋範圍**: Service層、Util層
- **測試數據**: 使用測試專用數據庫

### 15.2 集成測試
- **數據庫測試**: 測試數據訪問層功能
- **Web自動化測試**: 測試完整的抓取流程
- **API測試**: 測試REST接口功能

### 15.3 測試用例
```java
@Test
void testBondPriceInsert() {
    BondPrice record = new BondPrice();
    record.setSin("XS0852986313");
    record.setPrice("98.50");
    record.setGetDate(LocalDateTime.now());

    int result = bondPriceService.insert(record);
    assertEquals(1, result);
}
```

## 16. 運維監控

### 16.1 日誌管理
- **日誌框架**: Logback
- **日誌級別**: DEBUG, INFO, WARN, ERROR
- **日誌輪轉**: 按日期和大小輪轉
- **日誌路徑**: ./logs/bot-base.log

### 16.2 監控指標
- **系統健康**: 心跳檢測每5秒執行
- **任務執行**: 記錄每次抓取任務的執行狀態
- **錯誤統計**: 統計各類異常的發生頻率
- **性能指標**: 監控響應時間和資源使用情況

### 16.3 告警機制
- **郵件告警**: 系統異常時發送郵件通知
- **日誌告警**: 錯誤日誌達到閾值時觸發告警
- **業務告警**: 數據抓取失敗時發送通知

## 17. 安全考慮

### 17.1 數據安全
- **密碼加密**: 配置文件中的敏感信息加密存儲
- **SQL注入防護**: 使用參數化查詢防止SQL注入
- **XSS防護**: 對用戶輸入進行適當的轉義和驗證

### 17.2 系統安全
- **訪問控制**: 限制對敏感接口的訪問
- **審計日誌**: 記錄所有重要操作的審計信息
- **網絡安全**: 使用HTTPS協議進行數據傳輸

## 18. 維護手冊

### 18.1 常見問題處理
1. **ChromeDriver版本不匹配**: 更新ChromeDriver到匹配的版本
2. **網站結構變更**: 更新XPath定位器
3. **登錄失敗**: 檢查用戶名密碼是否正確
4. **數據庫連接失敗**: 檢查數據庫服務狀態和網絡連接

### 18.2 系統升級
1. **備份數據**: 升級前備份數據庫和配置文件
2. **停止服務**: 優雅關閉應用程序
3. **部署新版本**: 替換JAR文件
4. **驗證功能**: 執行測試用例驗證功能正常

### 18.3 故障恢復
1. **數據恢復**: 從備份恢復數據庫
2. **配置恢復**: 恢復配置文件
3. **服務重啟**: 重新啟動應用程序
4. **功能驗證**: 確認所有功能正常運行
